import type { Metadata } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "ALLPLAST - Vertical Injection Molding Machines | Amrut Brothers Machinery",
  description: "Leading manufacturer of vertical injection molding machines with 40+ years of experience. Specializing in Compact (15-50 tons), Standard (60-150 tons), and Heavy Duty (180-250 tons) series. CE certified with Double Slide Technology.",
  keywords: [
    "vertical injection molding machines",
    "ALLPLAST",
    "Amrut Brothers Machinery",
    "injection molding",
    "plastic machinery",
    "Double Slide Technology",
    "CE certified",
    "precision molding",
    "cosmetic tube shoulders",
    "toothpaste components"
  ],
  authors: [{ name: "Amrut Brothers Machinery Pvt Ltd" }],
  creator: "ALLPLAST",
  publisher: "Amrut Brothers Machinery Pvt Ltd",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://allplast.com',
    title: 'ALLPLAST - Vertical Injection Molding Machines',
    description: 'Leading manufacturer of vertical injection molding machines with 40+ years of experience and cutting-edge Double Slide Technology.',
    siteName: 'ALLPLAST',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'ALLPLAST - Vertical Injection Molding Machines',
    description: 'Leading manufacturer of vertical injection molding machines with 40+ years of experience.',
  },
  viewport: {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 1,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
