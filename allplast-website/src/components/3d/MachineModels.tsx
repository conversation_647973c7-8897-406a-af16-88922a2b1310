'use client';

import { useRef } from 'react';
import { useFrame } from '@react-three/fiber';
import { motion } from 'framer-motion-3d';
import { Mesh } from 'three';
import { MachineSeriesId } from '@/types';
import { MACHINE_SERIES } from '@/constants';

interface MachineModelProps {
  seriesId: MachineSeriesId;
  isActive: boolean;
  position?: [number, number, number];
}

function CompactSeriesModel({ isActive, position = [0, 0, 0] }: { isActive: boolean; position?: [number, number, number] }) {
  const meshRef = useRef<Mesh>(null);
  
  useFrame((state) => {
    if (meshRef.current && isActive) {
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;
    }
  });

  return (
    <motion.group
      position={position}
      initial={{ scale: 0, opacity: 0 }}
      animate={{ 
        scale: isActive ? 1 : 0.8, 
        opacity: isActive ? 1 : 0.3,
        y: isActive ? 0 : -1
      }}
      transition={{ duration: 0.8, ease: "easeInOut" }}
    >
      {/* Main Body - Compact and sleek */}
      <mesh ref={meshRef} position={[0, 0, 0]} castShadow receiveShadow>
        <boxGeometry args={[1.5, 2, 1]} />
        <meshStandardMaterial 
          color={MACHINE_SERIES.COMPACT.color}
          metalness={0.7}
          roughness={0.3}
        />
      </mesh>
      
      {/* Control Panel */}
      <mesh position={[0.8, 0.5, 0]} castShadow>
        <boxGeometry args={[0.2, 0.8, 0.6]} />
        <meshStandardMaterial 
          color="#1a1a1a"
          metalness={0.9}
          roughness={0.1}
        />
      </mesh>
      
      {/* Base */}
      <mesh position={[0, -1.2, 0]} castShadow receiveShadow>
        <cylinderGeometry args={[1.2, 1.2, 0.4, 8]} />
        <meshStandardMaterial 
          color="#333333"
          metalness={0.8}
          roughness={0.2}
        />
      </mesh>
      
      {/* Injection Unit */}
      <mesh position={[0, 0.8, 0.6]} castShadow>
        <cylinderGeometry args={[0.15, 0.15, 0.8, 16]} />
        <meshStandardMaterial 
          color="#4A90E2"
          metalness={0.6}
          roughness={0.4}
        />
      </mesh>
    </motion.group>
  );
}

function StandardSeriesModel({ isActive, position = [0, 0, 0] }: { isActive: boolean; position?: [number, number, number] }) {
  const meshRef = useRef<Mesh>(null);
  
  useFrame((state) => {
    if (meshRef.current && isActive) {
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.3) * 0.15;
    }
  });

  return (
    <motion.group
      position={position}
      initial={{ scale: 0, opacity: 0 }}
      animate={{ 
        scale: isActive ? 1 : 0.8, 
        opacity: isActive ? 1 : 0.3,
        y: isActive ? 0 : -1
      }}
      transition={{ duration: 0.8, ease: "easeInOut" }}
    >
      {/* Main Body - Standard size */}
      <mesh ref={meshRef} position={[0, 0, 0]} castShadow receiveShadow>
        <boxGeometry args={[2, 2.5, 1.2]} />
        <meshStandardMaterial 
          color={MACHINE_SERIES.STANDARD.color}
          metalness={0.7}
          roughness={0.3}
        />
      </mesh>
      
      {/* Control Panel */}
      <mesh position={[1.1, 0.5, 0]} castShadow>
        <boxGeometry args={[0.2, 1, 0.8]} />
        <meshStandardMaterial 
          color="#1a1a1a"
          metalness={0.9}
          roughness={0.1}
        />
      </mesh>
      
      {/* Base */}
      <mesh position={[0, -1.5, 0]} castShadow receiveShadow>
        <cylinderGeometry args={[1.5, 1.5, 0.5, 8]} />
        <meshStandardMaterial 
          color="#333333"
          metalness={0.8}
          roughness={0.2}
        />
      </mesh>
      
      {/* Injection Unit */}
      <mesh position={[0, 1, 0.7]} castShadow>
        <cylinderGeometry args={[0.2, 0.2, 1, 16]} />
        <meshStandardMaterial 
          color="#4A90E2"
          metalness={0.6}
          roughness={0.4}
        />
      </mesh>
      
      {/* Hydraulic System */}
      <mesh position={[-0.8, -0.5, 0]} castShadow>
        <boxGeometry args={[0.6, 1.5, 0.8]} />
        <meshStandardMaterial 
          color="#2559A3"
          metalness={0.5}
          roughness={0.5}
        />
      </mesh>
    </motion.group>
  );
}

function HeavyDutySeriesModel({ isActive, position = [0, 0, 0] }: { isActive: boolean; position?: [number, number, number] }) {
  const meshRef = useRef<Mesh>(null);
  
  useFrame((state) => {
    if (meshRef.current && isActive) {
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.2) * 0.08;
    }
  });

  return (
    <motion.group
      position={position}
      initial={{ scale: 0, opacity: 0 }}
      animate={{ 
        scale: isActive ? 1 : 0.8, 
        opacity: isActive ? 1 : 0.3,
        y: isActive ? 0 : -1
      }}
      transition={{ duration: 0.8, ease: "easeInOut" }}
    >
      {/* Main Body - Heavy duty and robust */}
      <mesh ref={meshRef} position={[0, 0, 0]} castShadow receiveShadow>
        <boxGeometry args={[2.5, 3, 1.5]} />
        <meshStandardMaterial 
          color={MACHINE_SERIES.HEAVY_DUTY.color}
          metalness={0.7}
          roughness={0.3}
        />
      </mesh>
      
      {/* Control Panel */}
      <mesh position={[1.3, 0.8, 0]} castShadow>
        <boxGeometry args={[0.3, 1.2, 1]} />
        <meshStandardMaterial 
          color="#1a1a1a"
          metalness={0.9}
          roughness={0.1}
        />
      </mesh>
      
      {/* Base - Extra sturdy */}
      <mesh position={[0, -1.8, 0]} castShadow receiveShadow>
        <cylinderGeometry args={[2, 2, 0.6, 8]} />
        <meshStandardMaterial 
          color="#333333"
          metalness={0.8}
          roughness={0.2}
        />
      </mesh>
      
      {/* Injection Unit - Larger */}
      <mesh position={[0, 1.2, 0.8]} castShadow>
        <cylinderGeometry args={[0.25, 0.25, 1.2, 16]} />
        <meshStandardMaterial 
          color="#4A90E2"
          metalness={0.6}
          roughness={0.4}
        />
      </mesh>
      
      {/* Hydraulic System - Dual units */}
      <mesh position={[-1, -0.5, 0]} castShadow>
        <boxGeometry args={[0.8, 2, 1]} />
        <meshStandardMaterial 
          color="#2559A3"
          metalness={0.5}
          roughness={0.5}
        />
      </mesh>
      
      <mesh position={[1, -0.5, 0]} castShadow>
        <boxGeometry args={[0.8, 2, 1]} />
        <meshStandardMaterial 
          color="#2559A3"
          metalness={0.5}
          roughness={0.5}
        />
      </mesh>
      
      {/* Support Structure */}
      <mesh position={[0, -0.5, -0.8]} castShadow>
        <boxGeometry args={[2.2, 2.5, 0.3]} />
        <meshStandardMaterial 
          color="#666666"
          metalness={0.6}
          roughness={0.4}
        />
      </mesh>
    </motion.group>
  );
}

export function MachineModel({ seriesId, isActive, position }: MachineModelProps) {
  switch (seriesId) {
    case 'COMPACT':
      return <CompactSeriesModel isActive={isActive} position={position} />;
    case 'STANDARD':
      return <StandardSeriesModel isActive={isActive} position={position} />;
    case 'HEAVY_DUTY':
      return <HeavyDutySeriesModel isActive={isActive} position={position} />;
    default:
      return <StandardSeriesModel isActive={isActive} position={position} />;
  }
}
