'use client';

import { useState, useEffect } from 'react';
import { Scene } from './Scene';
import { MachineModel } from './MachineModels';
import { ModelViewerProps, MachineSeriesId } from '@/types';
import { MACHINE_SERIES } from '@/constants';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, RotateCcw, ZoomIn, ZoomOut, Move3D } from 'lucide-react';
import { Button } from '@/components/ui/button';

export function ModelViewer({ selectedSeries, isLoading, onLoadingChange }: ModelViewerProps) {
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);

  // Simulate loading progress with error handling
  useEffect(() => {
    if (isLoading) {
      setLoadingProgress(0);
      setError(null);

      const interval = setInterval(() => {
        setLoadingProgress(prev => {
          if (prev >= 100) {
            clearInterval(interval);
            setTimeout(() => onLoadingChange(false), 200);
            return 100;
          }
          return prev + Math.random() * 15;
        });
      }, 100);

      // Simulate potential error (5% chance)
      const errorTimeout = setTimeout(() => {
        if (Math.random() < 0.05) {
          setError('Failed to load 3D model. Please try again.');
          clearInterval(interval);
          onLoadingChange(false);
        }
      }, 3000);

      return () => {
        clearInterval(interval);
        clearTimeout(errorTimeout);
      };
    }
  }, [isLoading, onLoadingChange]);

  // Handle series transition
  useEffect(() => {
    setIsTransitioning(true);
    const timer = setTimeout(() => setIsTransitioning(false), 800);
    return () => clearTimeout(timer);
  }, [selectedSeries]);

  const currentSeries = MACHINE_SERIES[selectedSeries];

  return (
    <div className="w-full h-full relative">
      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
          <Card className="p-6 text-center">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-primary" />
            <h3 className="text-lg font-semibold mb-2">Loading 3D Model</h3>
            <p className="text-muted-foreground mb-4">
              Preparing {currentSeries.name}...
            </p>
            <div className="w-48 bg-muted rounded-full h-2 mb-2">
              <div
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{ width: `${loadingProgress}%` }}
              />
            </div>
            <p className="text-sm text-muted-foreground">
              {Math.round(loadingProgress)}%
            </p>
          </Card>
        </div>
      )}

      {/* Error Overlay */}
      {error && (
        <div className="absolute inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
          <Card className="p-6 text-center max-w-md">
            <div className="w-12 h-12 bg-destructive/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-destructive text-xl">⚠️</span>
            </div>
            <h3 className="text-lg font-semibold mb-2 text-destructive">Error Loading Model</h3>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button
              onClick={() => {
                setError(null);
                onLoadingChange(true);
              }}
              variant="outline"
            >
              Try Again
            </Button>
          </Card>
        </div>
      )}

      {/* Model Information Panel */}
      <div className="absolute top-4 left-4 z-10">
        <Card className="p-4 bg-background/90 backdrop-blur-sm">
          <div className="flex items-center gap-2 mb-2">
            <Badge 
              variant="default" 
              className="bg-primary text-primary-foreground"
            >
              {currentSeries.range}
            </Badge>
            {isTransitioning && (
              <Loader2 className="w-4 h-4 animate-spin text-primary" />
            )}
          </div>
          <h3 className="font-semibold text-lg">{currentSeries.name}</h3>
          <p className="text-sm text-muted-foreground">
            {currentSeries.description}
          </p>
        </Card>
      </div>

      {/* Controls Panel */}
      <div className="absolute top-4 right-4 z-10">
        <Card className="p-2 bg-background/90 backdrop-blur-sm">
          <div className="flex flex-col gap-2">
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <RotateCcw className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <ZoomIn className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <ZoomOut className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <Move3D className="w-4 h-4" />
            </Button>
          </div>
        </Card>
      </div>

      {/* Instructions */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-10">
        <Card className="p-3 bg-background/90 backdrop-blur-sm">
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <span className="flex items-center gap-1">
              <Move3D className="w-4 h-4" />
              Drag to rotate
            </span>
            <span className="flex items-center gap-1">
              <ZoomIn className="w-4 h-4" />
              Scroll to zoom
            </span>
            <span className="hidden md:flex items-center gap-1">
              Right-click + drag to pan
            </span>
          </div>
        </Card>
      </div>

      {/* 3D Scene */}
      <Scene className="w-full h-full">
        {/* Render all models but only show the active one prominently */}
        {Object.keys(MACHINE_SERIES).map((seriesId) => (
          <MachineModel
            key={seriesId}
            seriesId={seriesId as MachineSeriesId}
            isActive={seriesId === selectedSeries}
            position={seriesId === selectedSeries ? [0, 0, 0] : [0, -5, 0]}
          />
        ))}
      </Scene>
    </div>
  );
}
