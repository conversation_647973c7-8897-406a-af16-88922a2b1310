'use client';

import { Canvas } from '@react-three/fiber';
import { OrbitControls, Environment, Grid, ContactShadows } from '@react-three/drei';
import { Suspense } from 'react';
import { SCENE_CONFIG } from '@/constants';

interface SceneProps {
  children: React.ReactNode;
  className?: string;
}

function SceneLighting() {
  return (
    <>
      {/* Ambient Light */}
      <ambientLight intensity={SCENE_CONFIG.lighting.ambient.intensity} />
      
      {/* Directional Light (Main light source) */}
      <directionalLight
        position={SCENE_CONFIG.lighting.directional.position}
        intensity={SCENE_CONFIG.lighting.directional.intensity}
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
        shadow-camera-far={50}
        shadow-camera-left={-10}
        shadow-camera-right={10}
        shadow-camera-top={10}
        shadow-camera-bottom={-10}
      />
      
      {/* Point Light for additional illumination */}
      <pointLight
        position={SCENE_CONFIG.lighting.point.position}
        intensity={SCENE_CONFIG.lighting.point.intensity}
        color="#ffffff"
      />
      
      {/* Fill light from the opposite side */}
      <pointLight
        position={[-5, 3, -5]}
        intensity={0.3}
        color="#4A90E2"
      />
    </>
  );
}

function SceneEnvironment() {
  return (
    <>
      {/* Environment for realistic reflections */}
      <Environment preset="warehouse" />
      
      {/* Ground grid */}
      <Grid
        renderOrder={-1}
        position={[0, -2, 0]}
        infiniteGrid
        cellSize={0.6}
        cellThickness={0.6}
        sectionSize={3.3}
        sectionThickness={1.5}
        sectionColor="#4A90E2"
        fadeDistance={30}
      />
      
      {/* Contact shadows for better ground interaction */}
      <ContactShadows
        position={[0, -1.99, 0]}
        opacity={0.4}
        scale={20}
        blur={1.5}
        far={4.5}
      />
    </>
  );
}

function SceneControls() {
  return (
    <OrbitControls
      enableDamping={SCENE_CONFIG.controls.enableDamping}
      dampingFactor={SCENE_CONFIG.controls.dampingFactor}
      enableZoom={SCENE_CONFIG.controls.enableZoom}
      enablePan={SCENE_CONFIG.controls.enablePan}
      enableRotate={SCENE_CONFIG.controls.enableRotate}
      maxDistance={SCENE_CONFIG.controls.maxDistance}
      minDistance={SCENE_CONFIG.controls.minDistance}
      maxPolarAngle={Math.PI / 2.2} // Prevent camera from going below ground
      minPolarAngle={Math.PI / 6} // Prevent camera from going too high
    />
  );
}



export function Scene({ children, className = '' }: SceneProps) {
  return (
    <div className={`w-full h-full ${className}`}>
      <Canvas
        camera={{
          position: SCENE_CONFIG.camera.position,
          fov: SCENE_CONFIG.camera.fov,
        }}
        shadows
        gl={{
          antialias: true,
          alpha: true,
          powerPreference: "high-performance",
          preserveDrawingBuffer: false, // Better performance
          failIfMajorPerformanceCaveat: false,
        }}
        dpr={[1, 2]} // Device pixel ratio for crisp rendering
        performance={{ min: 0.5 }} // Adaptive performance
        frameloop="demand" // Only render when needed
      >
        <Suspense fallback={null}>
          {/* Scene Lighting */}
          <SceneLighting />
          
          {/* Scene Environment */}
          <SceneEnvironment />
          
          {/* Scene Controls */}
          <SceneControls />
          
          {/* Children (3D Models) */}
          {children}
        </Suspense>
      </Canvas>
    </div>
  );
}
