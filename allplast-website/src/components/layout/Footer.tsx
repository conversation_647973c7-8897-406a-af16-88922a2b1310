'use client';

import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { COMPANY_INFO, MACHINE_SERIES } from '@/constants';
import { FooterProps } from '@/types';
import { motion } from 'framer-motion';
import { 
  Mail, 
  Phone, 
  MapPin, 
  Globe, 
  Award, 
  Settings, 
  Zap,
  Factory
} from 'lucide-react';

export function Footer({ className = '' }: FooterProps) {
  return (
    <footer className={`w-full bg-muted/30 border-t border-border ${className}`}>
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          
          {/* Company Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h3 className="text-xl font-bold text-primary mb-4">
              {COMPANY_INFO.name}
            </h3>
            <p className="text-sm text-muted-foreground mb-4">
              {COMPANY_INFO.fullName}
            </p>
            <div className="space-y-2 mb-4">
              <Badge variant="secondary" className="mr-2">
                {COMPANY_INFO.experience} Experience
              </Badge>
              <Badge variant="outline">
                {COMPANY_INFO.certifications[0]}
              </Badge>
            </div>
            <p className="text-sm text-foreground font-medium">
              Leading manufacturer of {COMPANY_INFO.specialization.toLowerCase()} 
              with cutting-edge technology and precision engineering.
            </p>
          </motion.div>

          {/* Machine Series */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
          >
            <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
              <Factory className="w-5 h-5 text-primary" />
              Machine Series
            </h3>
            <div className="space-y-3">
              {Object.values(MACHINE_SERIES).map((series, index) => (
                <div key={series.id} className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-foreground">
                      {series.name}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {series.range}
                    </p>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {series.range.split('-')[1]}
                  </Badge>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Key Features & Applications */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
              <Settings className="w-5 h-5 text-primary" />
              Key Features
            </h3>
            <div className="space-y-2 mb-6">
              {COMPANY_INFO.features.map((feature, index) => (
                <div key={index} className="flex items-center gap-2">
                  <Zap className="w-3 h-3 text-primary flex-shrink-0" />
                  <span className="text-sm text-foreground">{feature}</span>
                </div>
              ))}
            </div>
            
            <h4 className="text-md font-medium text-foreground mb-3">Applications</h4>
            <div className="space-y-2">
              {COMPANY_INFO.applications.map((application, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-secondary rounded-full flex-shrink-0" />
                  <span className="text-sm text-muted-foreground">{application}</span>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Contact Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
              <Mail className="w-5 h-5 text-primary" />
              Contact Us
            </h3>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Phone className="w-4 h-4 text-primary flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-foreground">Phone</p>
                  <p className="text-sm text-muted-foreground">+91 XXX XXX XXXX</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <Mail className="w-4 h-4 text-primary flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-foreground">Email</p>
                  <p className="text-sm text-muted-foreground"><EMAIL></p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <MapPin className="w-4 h-4 text-primary flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-foreground">Location</p>
                  <p className="text-sm text-muted-foreground">India</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <Globe className="w-4 h-4 text-primary flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-foreground">Website</p>
                  <p className="text-sm text-muted-foreground">www.allplast.com</p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Technology Highlight */}
        <motion.div
          className="mt-12 pt-8 border-t border-border"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <Card className="p-6 bg-primary/5 border-primary/20">
            <div className="flex items-center gap-3 mb-3">
              <Award className="w-6 h-6 text-primary" />
              <h4 className="text-lg font-semibold text-primary">
                {COMPANY_INFO.keyTechnology}
              </h4>
            </div>
            <p className="text-sm text-muted-foreground">
              Our proprietary Double Slide Technology represents the pinnacle of injection molding innovation, 
              delivering unmatched precision and efficiency for complex plastic component manufacturing.
            </p>
          </Card>
        </motion.div>

        {/* Copyright */}
        <motion.div
          className="mt-8 pt-6 border-t border-border text-center"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          viewport={{ once: true }}
        >
          <p className="text-sm text-muted-foreground">
            © 2024 {COMPANY_INFO.fullName}. All rights reserved. | 
            Precision Engineering • Quality Manufacturing • Innovation Excellence
          </p>
        </motion.div>
      </div>
    </footer>
  );
}
