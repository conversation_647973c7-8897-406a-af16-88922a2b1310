'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MACHINE_SERIES, COMPANY_INFO } from '@/constants';
import { HeaderProps, MachineSeriesId } from '@/types';
import { motion } from 'framer-motion';
import { Factory, Award, Zap } from 'lucide-react';

export function Header({ onSeriesSelect, selectedSeries }: HeaderProps) {
  const seriesEntries = Object.entries(MACHINE_SERIES) as [MachineSeriesId, typeof MACHINE_SERIES[MachineSeriesId]][];

  const getSeriesIcon = (seriesId: MachineSeriesId) => {
    switch (seriesId) {
      case 'COMPACT':
        return <Zap className="w-5 h-5" />;
      case 'STANDARD':
        return <Factory className="w-5 h-5" />;
      case 'HEAVY_DUTY':
        return <Award className="w-5 h-5" />;
      default:
        return <Factory className="w-5 h-5" />;
    }
  };

  return (
    <header className="w-full bg-background border-b border-border">
      <div className="container mx-auto px-4 py-6">
        {/* Company Branding */}
        <motion.div 
          className="text-center mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl md:text-5xl font-bold text-primary mb-2">
            {COMPANY_INFO.name}
          </h1>
          <p className="text-lg text-muted-foreground mb-2">
            {COMPANY_INFO.fullName}
          </p>
          <div className="flex flex-wrap justify-center gap-2 mb-4">
            <Badge variant="secondary" className="text-sm">
              {COMPANY_INFO.experience} Experience
            </Badge>
            <Badge variant="outline" className="text-sm">
              {COMPANY_INFO.certifications[0]}
            </Badge>
            <Badge variant="outline" className="text-sm">
              {COMPANY_INFO.keyTechnology}
            </Badge>
          </div>
          <p className="text-xl font-semibold text-foreground">
            {COMPANY_INFO.specialization}
          </p>
        </motion.div>

        {/* Machine Series Navigation */}
        <motion.div 
          className="flex flex-col md:flex-row justify-center items-center gap-4 md:gap-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {seriesEntries.map(([seriesId, series], index) => (
            <motion.div
              key={seriesId}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.4, delay: 0.3 + index * 0.1 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                variant={selectedSeries === seriesId ? "default" : "outline"}
                size="lg"
                onClick={() => onSeriesSelect(seriesId)}
                className={`
                  flex flex-col items-center justify-center 
                  h-24 w-48 md:w-56 
                  text-center transition-all duration-300
                  ${selectedSeries === seriesId 
                    ? 'bg-primary text-primary-foreground shadow-lg' 
                    : 'hover:bg-primary/10 hover:border-primary'
                  }
                `}
              >
                <div className="flex items-center gap-2 mb-1">
                  {getSeriesIcon(seriesId)}
                  <span className="font-semibold text-base">
                    {series.name}
                  </span>
                </div>
                <span className="text-sm opacity-90 font-medium">
                  {series.range}
                </span>
                <span className="text-xs opacity-75 mt-1">
                  {series.description}
                </span>
              </Button>
            </motion.div>
          ))}
        </motion.div>

        {/* Key Features */}
        <motion.div 
          className="mt-8 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <div className="flex flex-wrap justify-center gap-4 text-sm text-muted-foreground">
            {COMPANY_INFO.features.map((feature, index) => (
              <span key={index} className="flex items-center gap-1">
                <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                {feature}
              </span>
            ))}
          </div>
        </motion.div>
      </div>
    </header>
  );
}
