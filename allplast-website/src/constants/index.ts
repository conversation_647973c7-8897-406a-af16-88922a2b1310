// Machine Series Configuration
export const MACHINE_SERIES = {
  COMPACT: {
    id: 'compact',
    name: 'Compact Series',
    range: '15-50 tons',
    description: 'Perfect for small to medium precision parts',
    color: '#2559A3', // Primary color
  },
  STANDARD: {
    id: 'standard',
    name: 'Standard Series',
    range: '60-150 tons',
    description: 'Versatile machines for various applications',
    color: '#4A90E2', // Lighter blue
  },
  HEAVY_DUTY: {
    id: 'heavy-duty',
    name: 'Heavy Duty Series',
    range: '180-250 tons',
    description: 'High-capacity machines for demanding applications',
    color: '#D93731', // Secondary color
  },
} as const;

// Company Information
export const COMPANY_INFO = {
  name: 'ALLPLAST',
  fullName: 'Amrut Brothers Machinery Pvt Ltd',
  experience: '40+ years',
  specialization: 'Vertical Injection Molding Machines',
  certifications: ['CE Certified'],
  keyTechnology: 'Double Slide Technology (ABS-VV-DS series)',
  applications: [
    'Cosmetic tube shoulders',
    'Toothpaste components',
    'Precision plastic parts',
  ],
  features: [
    'Precision hydraulic systems',
    'Energy efficiency',
    'Advanced automation',
    'Quality manufacturing',
  ],
} as const;

// 3D Scene Configuration
export const SCENE_CONFIG = {
  camera: {
    position: [5, 5, 5] as [number, number, number],
    fov: 50,
  },
  lighting: {
    ambient: {
      intensity: 0.4,
    },
    directional: {
      intensity: 1,
      position: [10, 10, 5] as [number, number, number],
    },
    point: {
      intensity: 0.8,
      position: [0, 5, 0] as [number, number, number],
    },
  },
  controls: {
    enableDamping: true,
    dampingFactor: 0.05,
    enableZoom: true,
    enablePan: true,
    enableRotate: true,
    maxDistance: 20,
    minDistance: 2,
  },
} as const;

// Animation Configuration
export const ANIMATION_CONFIG = {
  modelTransition: {
    duration: 0.8,
    ease: 'easeInOut',
  },
  buttonHover: {
    scale: 1.05,
    duration: 0.2,
  },
  loading: {
    duration: 1.5,
    repeat: Infinity,
  },
} as const;

// Responsive Breakpoints
export const BREAKPOINTS = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
} as const;
