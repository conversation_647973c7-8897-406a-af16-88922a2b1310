'use client';

import { useState, useCallback } from 'react';
import { MachineSeriesId } from '@/types';

export function useModelViewer() {
  const [selectedSeries, setSelectedSeries] = useState<MachineSeriesId>('STANDARD');
  const [isLoading, setIsLoading] = useState(false);

  const handleSeriesSelect = useCallback((seriesId: MachineSeriesId) => {
    if (seriesId !== selectedSeries) {
      setIsLoading(true);
      setSelectedSeries(seriesId);
    }
  }, [selectedSeries]);

  const handleLoadingChange = useCallback((loading: boolean) => {
    setIsLoading(loading);
  }, []);

  return {
    selectedSeries,
    isLoading,
    handleSeriesSelect,
    handleLoadingChange,
  };
}
