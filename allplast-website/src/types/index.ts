import { MACHINE_SERIES } from '@/constants';

// Machine Series Types
export type MachineSeriesId = keyof typeof MACHINE_SERIES;

export interface MachineSeries {
  id: string;
  name: string;
  range: string;
  description: string;
  color: string;
}

// 3D Model Types
export interface Model3D {
  id: string;
  name: string;
  geometry: 'box' | 'cylinder' | 'sphere' | 'cone';
  dimensions: {
    width: number;
    height: number;
    depth: number;
  };
  position: [number, number, number];
  rotation: [number, number, number];
  color: string;
  metalness?: number;
  roughness?: number;
}

// Component Props Types
export interface HeaderProps {
  onSeriesSelect: (seriesId: MachineSeriesId) => void;
  selectedSeries: MachineSeriesId;
}

export interface ModelViewerProps {
  selectedSeries: MachineSeriesId;
  isLoading: boolean;
  onLoadingChange: (loading: boolean) => void;
}

export interface FooterProps {
  className?: string;
}

// Animation Types
export interface AnimationState {
  isTransitioning: boolean;
  currentModel: MachineSeriesId;
  previousModel: MachineSeriesId | null;
}

// Loading States
export interface LoadingState {
  isLoading: boolean;
  progress: number;
  error: string | null;
}

// Scene Configuration Types
export interface CameraConfig {
  position: [number, number, number];
  fov: number;
}

export interface LightingConfig {
  ambient: {
    intensity: number;
  };
  directional: {
    intensity: number;
    position: [number, number, number];
  };
  point: {
    intensity: number;
    position: [number, number, number];
  };
}

export interface ControlsConfig {
  enableDamping: boolean;
  dampingFactor: number;
  enableZoom: boolean;
  enablePan: boolean;
  enableRotate: boolean;
  maxDistance: number;
  minDistance: number;
}

export interface SceneConfig {
  camera: CameraConfig;
  lighting: LightingConfig;
  controls: ControlsConfig;
}
